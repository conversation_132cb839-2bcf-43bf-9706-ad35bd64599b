import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: Request) {
  const { userId } = await auth();

  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  const formData = await request.formData();
  const file = formData.get('file') as File;

  if (!file) {
    return new NextResponse('No file found', { status: 400 });
  }

  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
  ];
  if (!allowedTypes.includes(file.type)) {
    return new NextResponse('File type not allowed', { status: 400 });
  }

  // For now, we'll just log the file details.
  // In the next step, we'll upload it to a cloud storage service.
  console.log('Received file:', file.name, file.size, file.type);

  // Here you would typically upload the file to S3 or other cloud storage
  // and create a job record in the database.

  return NextResponse.json({ success: true });
}
